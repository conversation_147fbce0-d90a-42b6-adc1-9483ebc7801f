using UnityEngine;
using UnityEditor;
using Game.VFX;

namespace GameEditor.VFX
{
    /// <summary>
    /// EarthboundBackgroundController的自定义编辑器
    /// </summary>
    [CustomEditor(typeof(EarthboundBackgroundController))]
    public class EarthboundBackgroundControllerEditor : Editor
    {
        private EarthboundBackgroundController _controller;
        private bool _showTextureSection = true; // 显示纹理部分
        private bool _showAnimationSection = true; // 显示动画部分
        private bool _showColorSection = true; // 显示颜色部分
        private bool _showPresetSection = true; // 显示预设部分
        
        private void OnEnable()
        {
            _controller = (EarthboundBackgroundController)target;
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            // 标题
            EditorGUILayout.LabelField("Earthbound Background Controller", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 纹理设置部分
            _showTextureSection = EditorGUILayout.BeginFoldoutHeaderGroup(_showTextureSection, "纹理设置");
            if (_showTextureSection)
            {
                EditorGUI.indentLevel++;
                
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_baseTexture"), new GUIContent("基础纹理 (BW)"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_paletteTexture"), new GUIContent("调色板纹理"));
                
                // 快速选择按钮
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("快速选择:", GUILayout.Width(60));
                
                if (GUILayout.Button("万花筒", GUILayout.Width(60)))
                {
                    LoadTextureQuick("kaleidoscope03");
                }
                if (GUILayout.Button("圆圈", GUILayout.Width(60)))
                {
                    LoadTextureQuick("circles05");
                }
                if (GUILayout.Button("线条", GUILayout.Width(60)))
                {
                    LoadTextureQuick("lines01");
                }
                if (GUILayout.Button("海洋", GUILayout.Width(60)))
                {
                    LoadTextureQuick("sea02");
                }
                
                EditorGUILayout.EndHorizontal();
                
                EditorGUI.indentLevel--;
            }
            EditorGUILayout.EndFoldoutHeaderGroup();
            
            EditorGUILayout.Space();
            
            // 动画设置部分
            _showAnimationSection = EditorGUILayout.BeginFoldoutHeaderGroup(_showAnimationSection, "动画设置");
            if (_showAnimationSection)
            {
                EditorGUI.indentLevel++;
                
                // 滚动设置
                EditorGUILayout.LabelField("滚动", EditorStyles.miniBoldLabel);
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_scrollSpeed"), new GUIContent("滚动速度"));
                
                EditorGUILayout.Space();
                
                // UV扭曲设置
                EditorGUILayout.LabelField("UV扭曲", EditorStyles.miniBoldLabel);
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_waveAmplitude"), new GUIContent("波动振幅"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_waveFrequency"), new GUIContent("波动频率"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_waveSpeed"), new GUIContent("波动速度"));
                
                EditorGUILayout.Space();
                
                // 调色板设置
                EditorGUILayout.LabelField("调色板", EditorStyles.miniBoldLabel);
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_paletteCycleSpeed"), new GUIContent("循环速度"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_paletteOffset"), new GUIContent("偏移"));
                
                EditorGUI.indentLevel--;
            }
            EditorGUILayout.EndFoldoutHeaderGroup();
            
            EditorGUILayout.Space();
            
            // 颜色调整部分
            _showColorSection = EditorGUILayout.BeginFoldoutHeaderGroup(_showColorSection, "颜色调整");
            if (_showColorSection)
            {
                EditorGUI.indentLevel++;
                
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_brightness"), new GUIContent("亮度"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_contrast"), new GUIContent("对比度"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("_saturation"), new GUIContent("饱和度"));
                
                EditorGUI.indentLevel--;
            }
            EditorGUILayout.EndFoldoutHeaderGroup();
            
            EditorGUILayout.Space();
            
            // 预设部分
            _showPresetSection = EditorGUILayout.BeginFoldoutHeaderGroup(_showPresetSection, "预设管理");
            if (_showPresetSection)
            {
                EditorGUI.indentLevel++;
                
                // 使用SerializedProperty显示和修改预设
                var presetProperty = serializedObject.FindProperty("_currentPreset");
                var oldPreset = presetProperty.objectReferenceValue as EarthboundBackgroundPreset;
                
                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(presetProperty, new GUIContent("当前预设"));
                
                // 如果预设改变了，应用新预设
                if (EditorGUI.EndChangeCheck())
                {
                    var newPreset = presetProperty.objectReferenceValue as EarthboundBackgroundPreset;
                    
                    // 先保存序列化更改
                    serializedObject.ApplyModifiedProperties();
                    
                    // 然后应用预设参数到材质
                    if (newPreset != null)
                    {
                        _controller.ApplyPreset(newPreset);
                    }
                    
                    // 标记场景为已修改
                    EditorUtility.SetDirty(_controller);
                    
                    // 刷新序列化对象
                    serializedObject.Update();
                }
                
                // 按钮行
                EditorGUILayout.BeginHorizontal();
                
                // 保存当前设置为预设
                if (GUILayout.Button("保存为新预设"))
                {
                    SaveCurrentAsPreset();
                }
                
                // 清除当前预设
                if (presetProperty.objectReferenceValue != null)
                {
                    if (GUILayout.Button("清除预设"))
                    {
                        presetProperty.objectReferenceValue = null;
                        serializedObject.ApplyModifiedProperties();
                        EditorUtility.SetDirty(_controller);
                    }
                }
                
                EditorGUILayout.EndHorizontal();
                
                EditorGUI.indentLevel--;
            }
            EditorGUILayout.EndFoldoutHeaderGroup();
            
            EditorGUILayout.Space();
            
            // 工具按钮
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("应用设置"))
            {
                serializedObject.ApplyModifiedProperties();
                _controller.ApplySettings();
                EditorUtility.SetDirty(_controller);
            }
            
            if (GUILayout.Button("重置为默认"))
            {
                ResetToDefault();
            }
            
            EditorGUILayout.EndHorizontal();
            
            // 确保所有更改都被保存
            if (GUI.changed)
            {
                serializedObject.ApplyModifiedProperties();
            }
            
            // 实时预览提示
            if (Application.isPlaying)
            {
                EditorGUILayout.HelpBox("正在实时预览效果", MessageType.Info);
            }
        }
        
        /// <summary>
        /// 快速加载纹理
        /// </summary>
        private void LoadTextureQuick(string textureName)
        {
            // 查找BW文件夹中的纹理
            string bwPath = $"Assets/Game/Sprites/UI/EleganceUI/Backgrounds/Animated Backgrounds/BW/{textureName}.png";
            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(bwPath);
            
            if (texture != null)
            {
                serializedObject.FindProperty("_baseTexture").objectReferenceValue = texture;
                serializedObject.ApplyModifiedProperties();
                _controller.ApplySettings();
            }
            else
            {
                Debug.LogWarning($"找不到纹理: {bwPath}");
            }
        }
        
        /// <summary>
        /// 保存当前设置为预设
        /// </summary>
        private void SaveCurrentAsPreset()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "保存背景预设",
                "EarthboundBackgroundPreset",
                "asset",
                "请输入预设文件名"
            );
            
            if (!string.IsNullOrEmpty(path))
            {
                var preset = ScriptableObject.CreateInstance<EarthboundBackgroundPreset>();
                preset.CopyFromController(_controller);
                
                AssetDatabase.CreateAsset(preset, path);
                AssetDatabase.SaveAssets();
                
                EditorUtility.DisplayDialog("成功", "预设已保存", "确定");
                Selection.activeObject = preset;
            }
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        private void ResetToDefault()
        {
            serializedObject.FindProperty("_scrollSpeed").vector2Value = new Vector2(0.1f, 0.1f);
            serializedObject.FindProperty("_waveAmplitude").vector2Value = new Vector2(0.1f, 0.1f);
            serializedObject.FindProperty("_waveFrequency").vector2Value = new Vector2(10f, 10f);
            serializedObject.FindProperty("_waveSpeed").vector2Value = new Vector2(1f, 1f);
            serializedObject.FindProperty("_paletteCycleSpeed").floatValue = 1f;
            serializedObject.FindProperty("_paletteOffset").floatValue = 0f;
            serializedObject.FindProperty("_brightness").floatValue = 1f;
            serializedObject.FindProperty("_contrast").floatValue = 1f;
            serializedObject.FindProperty("_saturation").floatValue = 1f;
            serializedObject.FindProperty("_currentPreset").objectReferenceValue = null; // 清除预设
            
            serializedObject.ApplyModifiedProperties();
            _controller.ApplySettings();
            EditorUtility.SetDirty(_controller);
        }
    }
}