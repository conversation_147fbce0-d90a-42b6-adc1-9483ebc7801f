using UnityEngine;

namespace Game.VFX
{
    /// <summary>
    /// Earthbound风格背景预设
    /// </summary>
    [CreateAssetMenu(fileName = "EarthboundBackgroundPreset", menuName = "Game/VFX/Earthbound Background Preset")]
    public class EarthboundBackgroundPreset : ScriptableObject
    {
        [Header("预设信息")]
        public string presetName = "New Preset"; // 预设名称
        [TextArea(3, 5)]
        public string description = ""; // 预设描述
        
        [Header("纹理设置")]
        public Texture2D baseTexture; // 黑白基础纹理
        public Texture2D paletteTexture; // 调色板纹理
        
        [Header("滚动设置")]
        public Vector2 scrollSpeed = new Vector2(0.1f, 0.1f); // 滚动速度
        
        [Header("UV扭曲设置")]
        public Vector2 waveAmplitude = new Vector2(0.1f, 0.1f); // 波动振幅
        public Vector2 waveFrequency = new Vector2(10f, 10f); // 波动频率
        public Vector2 waveSpeed = new Vector2(1f, 1f); // 波动速度
        
        [Header("调色板设置")]
        [Range(0f, 5f)]
        public float paletteCycleSpeed = 1f; // 调色板循环速度
        [Range(0f, 1f)]
        public float paletteOffset = 0f; // 调色板偏移
        
        [Header("颜色调整")]
        [Range(0f, 2f)]
        public float brightness = 1f; // 亮度
        [Range(0f, 2f)]
        public float contrast = 1f; // 对比度
        [Range(0f, 2f)]
        public float saturation = 1f; // 饱和度
        
        [Header("预览设置")]
        public Sprite previewSprite; // 预览图片
        
        /// <summary>
        /// 从控制器复制设置
        /// </summary>
        public void CopyFromController(EarthboundBackgroundController controller)
        {
            if (controller == null) return;
            
            // 复制所有参数
            var fields = controller.GetType().GetFields(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            foreach (var field in fields)
            {
                if (field.Name == "_baseTexture") baseTexture = field.GetValue(controller) as Texture2D;
                else if (field.Name == "_paletteTexture") paletteTexture = field.GetValue(controller) as Texture2D;
                else if (field.Name == "_scrollSpeed") scrollSpeed = (Vector2)field.GetValue(controller);
                else if (field.Name == "_waveAmplitude") waveAmplitude = (Vector2)field.GetValue(controller);
                else if (field.Name == "_waveFrequency") waveFrequency = (Vector2)field.GetValue(controller);
                else if (field.Name == "_waveSpeed") waveSpeed = (Vector2)field.GetValue(controller);
                else if (field.Name == "_paletteCycleSpeed") paletteCycleSpeed = (float)field.GetValue(controller);
                else if (field.Name == "_paletteOffset") paletteOffset = (float)field.GetValue(controller);
                else if (field.Name == "_brightness") brightness = (float)field.GetValue(controller);
                else if (field.Name == "_contrast") contrast = (float)field.GetValue(controller);
                else if (field.Name == "_saturation") saturation = (float)field.GetValue(controller);
            }
        }
        
        /// <summary>
        /// 验证预设数据
        /// </summary>
        private void OnValidate()
        {
            // 限制参数范围
            paletteCycleSpeed = Mathf.Max(0f, paletteCycleSpeed);
            paletteOffset = Mathf.Clamp01(paletteOffset);
            brightness = Mathf.Max(0f, brightness);
            contrast = Mathf.Max(0f, contrast);
            saturation = Mathf.Max(0f, saturation);
        }
    }
}