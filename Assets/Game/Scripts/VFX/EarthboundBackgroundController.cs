using UnityEngine;
using UnityEngine.UI;

namespace Game.VFX
{
    /// <summary>
    /// Earthbound风格背景控制器
    /// </summary>
    [RequireComponent(typeof(Renderer))]
    public class EarthboundBackgroundController : MonoBehaviour
    {
        #region 字段
        
        [Header("纹理设置")]
        [SerializeField] private Texture2D _baseTexture; // 黑白基础纹理
        [SerializeField] private Texture2D _paletteTexture; // 调色板纹理
        
        [Header("滚动设置")]
        [SerializeField] private Vector2 _scrollSpeed = new Vector2(0.1f, 0.1f); // 滚动速度
        
        [Header("UV扭曲设置")]
        [SerializeField] private Vector2 _waveAmplitude = new Vector2(0.1f, 0.1f); // 波动振幅
        [SerializeField] private Vector2 _waveFrequency = new Vector2(10f, 10f); // 波动频率
        [SerializeField] private Vector2 _waveSpeed = new Vector2(1f, 1f); // 波动速度
        
        [Header("调色板设置")]
        [SerializeField] private float _paletteCycleSpeed = 1f; // 调色板循环速度
        [SerializeField] private float _paletteOffset = 0f; // 调色板偏移
        
        [Header("颜色调整")]
        [SerializeField] private float _brightness = 1f; // 亮度
        [SerializeField] private float _contrast = 1f; // 对比度
        [SerializeField] private float _saturation = 1f; // 饱和度
        
        [Header("预设")]
        [SerializeField] private EarthboundBackgroundPreset _currentPreset; // 当前应用的预设
        
        private Material _material; // 材质实例
        private Renderer _renderer; // 渲染器组件
        
        // 着色器属性ID缓存
        private static readonly int MainTexProp = Shader.PropertyToID("_MainTex");
        private static readonly int PaletteTexProp = Shader.PropertyToID("_PaletteTex");
        private static readonly int ScrollSpeedProp = Shader.PropertyToID("_ScrollSpeed");
        private static readonly int WaveAmplitudeProp = Shader.PropertyToID("_WaveAmplitude");
        private static readonly int WaveFrequencyProp = Shader.PropertyToID("_WaveFrequency");
        private static readonly int WaveSpeedProp = Shader.PropertyToID("_WaveSpeed");
        private static readonly int PaletteCycleSpeedProp = Shader.PropertyToID("_PaletteCycleSpeed");
        private static readonly int PaletteOffsetProp = Shader.PropertyToID("_PaletteOffset");
        private static readonly int BrightnessProp = Shader.PropertyToID("_Brightness");
        private static readonly int ContrastProp = Shader.PropertyToID("_Contrast");
        private static readonly int SaturationProp = Shader.PropertyToID("_Saturation");
        
        #endregion
        
        #region 属性
        
        /// <summary>
        /// 当前应用的预设
        /// </summary>
        public EarthboundBackgroundPreset CurrentPreset => _currentPreset;
        
        #endregion
        
        #region Unity生命周期
        
        private void Awake()
        {
            // 获取渲染器组件
            _renderer = GetComponent<Renderer>();
            
            // 创建材质实例
            if (_renderer.sharedMaterial != null)
            {
                _material = new Material(_renderer.sharedMaterial);
                _renderer.material = _material;
            }
            else if (_renderer.material != null)
            {
                // 如果没有sharedMaterial但有material，使用现有材质
                _material = _renderer.material;
            }
        }
        
        private void Start()
        {
            // 如果有预设，优先应用预设
            if (_currentPreset != null)
            {
                ApplyPreset(_currentPreset);
            }
            else
            {
                // 否则应用当前设置
                ApplySettings();
            }
        }
        
        private void Update()
        {
            // 实时更新着色器参数（用于编辑器调试）
            #if UNITY_EDITOR
            if (_material != null)
            {
                ApplySettings();
            }
            #endif
        }
        
        private void OnDestroy()
        {
            // 清理材质实例
            if (_material != null)
            {
                Destroy(_material);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 应用设置到材质
        /// </summary>
        public void ApplySettings()
        {
            if (_material == null) return;
            
            // 设置纹理
            if (_baseTexture != null)
                _material.SetTexture(MainTexProp, _baseTexture);
            
            if (_paletteTexture != null)
                _material.SetTexture(PaletteTexProp, _paletteTexture);
            
            // 设置参数
            _material.SetVector(ScrollSpeedProp, _scrollSpeed);
            _material.SetVector(WaveAmplitudeProp, _waveAmplitude);
            _material.SetVector(WaveFrequencyProp, _waveFrequency);
            _material.SetVector(WaveSpeedProp, _waveSpeed);
            _material.SetFloat(PaletteCycleSpeedProp, _paletteCycleSpeed);
            _material.SetFloat(PaletteOffsetProp, _paletteOffset);
            _material.SetFloat(BrightnessProp, _brightness);
            _material.SetFloat(ContrastProp, _contrast);
            _material.SetFloat(SaturationProp, _saturation);
        }
        
        /// <summary>
        /// 设置基础纹理
        /// </summary>
        public void SetBaseTexture(Texture2D texture)
        {
            _baseTexture = texture;
            if (_material != null && texture != null)
            {
                _material.SetTexture(MainTexProp, texture);
            }
        }
        
        /// <summary>
        /// 设置调色板纹理
        /// </summary>
        public void SetPaletteTexture(Texture2D texture)
        {
            _paletteTexture = texture;
            if (_material != null && texture != null)
            {
                _material.SetTexture(PaletteTexProp, texture);
            }
        }
        
        /// <summary>
        /// 设置滚动速度
        /// </summary>
        public void SetScrollSpeed(Vector2 speed)
        {
            _scrollSpeed = speed;
            if (_material != null)
            {
                _material.SetVector(ScrollSpeedProp, speed);
            }
        }
        
        /// <summary>
        /// 设置波动参数
        /// </summary>
        public void SetWaveParameters(Vector2 amplitude, Vector2 frequency, Vector2 speed)
        {
            _waveAmplitude = amplitude;
            _waveFrequency = frequency;
            _waveSpeed = speed;
            
            if (_material != null)
            {
                _material.SetVector(WaveAmplitudeProp, amplitude);
                _material.SetVector(WaveFrequencyProp, frequency);
                _material.SetVector(WaveSpeedProp, speed);
            }
        }
        
        /// <summary>
        /// 设置调色板循环速度
        /// </summary>
        public void SetPaletteCycleSpeed(float speed)
        {
            _paletteCycleSpeed = speed;
            if (_material != null)
            {
                _material.SetFloat(PaletteCycleSpeedProp, speed);
            }
        }
        
        /// <summary>
        /// 设置颜色调整参数
        /// </summary>
        public void SetColorAdjustment(float brightness, float contrast, float saturation)
        {
            _brightness = brightness;
            _contrast = contrast;
            _saturation = saturation;
            
            if (_material != null)
            {
                _material.SetFloat(BrightnessProp, brightness);
                _material.SetFloat(ContrastProp, contrast);
                _material.SetFloat(SaturationProp, saturation);
            }
        }
        
        /// <summary>
        /// 从预设应用设置
        /// </summary>
        public void ApplyPreset(EarthboundBackgroundPreset preset)
        {
            if (preset == null) return;
            
            _baseTexture = preset.baseTexture;
            _paletteTexture = preset.paletteTexture;
            _scrollSpeed = preset.scrollSpeed;
            _waveAmplitude = preset.waveAmplitude;
            _waveFrequency = preset.waveFrequency;
            _waveSpeed = preset.waveSpeed;
            _paletteCycleSpeed = preset.paletteCycleSpeed;
            _paletteOffset = preset.paletteOffset;
            _brightness = preset.brightness;
            _contrast = preset.contrast;
            _saturation = preset.saturation;
            
            // 保存当前预设引用
            _currentPreset = preset;
            
            ApplySettings();
            
            // 在编辑器中标记为已修改，确保序列化
            #if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                UnityEditor.EditorUtility.SetDirty(this);
            }
            #endif
        }
        
        #endregion
    }
}