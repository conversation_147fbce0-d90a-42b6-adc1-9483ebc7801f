using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace Game.VFX
{
    /// <summary>
    /// Earthbound风格多层背景管理器
    /// </summary>
    public class EarthboundBackgroundManager : MonoBehaviour
    {
        #region 内部类
        
        [System.Serializable]
        public class BackgroundLayer
        {
            public string layerName = "Layer"; // 层名称
            public GameObject layerObject; // 层对象
            public EarthboundBackgroundController controller; // 控制器
            public EarthboundBackgroundPreset preset; // 预设
            [Range(0f, 1f)]
            public float opacity = 1f; // 透明度
            public bool isActive = true; // 是否激活
            public int sortingOrder = 0; // 排序顺序
        }
        
        [System.Serializable]
        public class BackgroundTransition
        {
            public string transitionName = "Transition"; // 过渡名称
            public float duration = 1f; // 过渡持续时间
            public AnimationCurve curve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f); // 过渡曲线
            public EarthboundBackgroundPreset targetPreset; // 目标预设
        }
        
        #endregion
        
        #region 字段
        
        [Header("层设置")]
        [SerializeField] private List<BackgroundLayer> _layers = new List<BackgroundLayer>(); // 背景层列表
        [SerializeField] private Material _backgroundMaterial; // 背景材质
        
        [Header("混合设置")]
        [SerializeField] private bool _enableBlending = true; // 启用混合
        [SerializeField] private Camera _targetCamera; // 目标相机
        
        [Header("事件")]
        public UnityEvent<string> onTransitionStart; // 过渡开始事件
        public UnityEvent<string> onTransitionComplete; // 过渡完成事件
        
        private Dictionary<string, BackgroundLayer> _layerDictionary; // 层字典
        private Coroutine _currentTransition; // 当前过渡协程
        
        #endregion
        
        #region Unity生命周期
        
        private void Awake()
        {
            // 初始化层字典
            InitializeLayerDictionary();
            
            // 如果没有指定相机，使用主相机
            if (_targetCamera == null)
            {
                _targetCamera = Camera.main;
            }
        }
        
        private void Start()
        {
            // 初始化所有层
            InitializeLayers();
        }
        
        #endregion
        
        #region 初始化方法
        
        /// <summary>
        /// 初始化层字典
        /// </summary>
        private void InitializeLayerDictionary()
        {
            _layerDictionary = new Dictionary<string, BackgroundLayer>();
            foreach (var layer in _layers)
            {
                if (!string.IsNullOrEmpty(layer.layerName) && !_layerDictionary.ContainsKey(layer.layerName))
                {
                    _layerDictionary.Add(layer.layerName, layer);
                }
            }
        }
        
        /// <summary>
        /// 初始化所有层
        /// </summary>
        private void InitializeLayers()
        {
            foreach (var layer in _layers)
            {
                if (layer.layerObject == null) continue;
                
                // 确保有控制器
                if (layer.controller == null)
                {
                    layer.controller = layer.layerObject.GetComponent<EarthboundBackgroundController>();
                    if (layer.controller == null)
                    {
                        layer.controller = layer.layerObject.AddComponent<EarthboundBackgroundController>();
                    }
                }
                
                // 应用预设
                if (layer.preset != null)
                {
                    layer.controller.ApplyPreset(layer.preset);
                }
                
                // 设置透明度
                SetLayerOpacity(layer, layer.opacity);
                
                // 设置激活状态
                layer.layerObject.SetActive(layer.isActive);
                
                // 设置排序顺序
                var renderer = layer.layerObject.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.sortingOrder = layer.sortingOrder;
                }
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 添加新层
        /// </summary>
        public BackgroundLayer AddLayer(string layerName, GameObject prefab, EarthboundBackgroundPreset preset = null)
        {
            // 创建层对象
            var layerObject = Instantiate(prefab, transform);
            layerObject.name = layerName;
            
            // 创建层数据
            var layer = new BackgroundLayer
            {
                layerName = layerName,
                layerObject = layerObject,
                controller = layerObject.GetComponent<EarthboundBackgroundController>() ?? layerObject.AddComponent<EarthboundBackgroundController>(),
                preset = preset,
                opacity = 1f,
                isActive = true,
                sortingOrder = _layers.Count
            };
            
            // 添加到列表和字典
            _layers.Add(layer);
            _layerDictionary[layerName] = layer;
            
            // 应用预设
            if (preset != null)
            {
                layer.controller.ApplyPreset(preset);
            }
            
            return layer;
        }
        
        /// <summary>
        /// 移除层
        /// </summary>
        public void RemoveLayer(string layerName)
        {
            if (_layerDictionary.TryGetValue(layerName, out var layer))
            {
                _layers.Remove(layer);
                _layerDictionary.Remove(layerName);
                
                if (layer.layerObject != null)
                {
                    Destroy(layer.layerObject);
                }
            }
        }
        
        /// <summary>
        /// 获取层
        /// </summary>
        public BackgroundLayer GetLayer(string layerName)
        {
            return _layerDictionary.TryGetValue(layerName, out var layer) ? layer : null;
        }
        
        /// <summary>
        /// 设置层透明度
        /// </summary>
        public void SetLayerOpacity(string layerName, float opacity)
        {
            var layer = GetLayer(layerName);
            if (layer != null)
            {
                SetLayerOpacity(layer, opacity);
            }
        }
        
        /// <summary>
        /// 设置层透明度
        /// </summary>
        private void SetLayerOpacity(BackgroundLayer layer, float opacity)
        {
            layer.opacity = Mathf.Clamp01(opacity);
            
            // 更新材质透明度
            if (layer.controller != null)
            {
                var renderer = layer.layerObject.GetComponent<Renderer>();
                if (renderer != null && renderer.material != null)
                {
                    var color = renderer.material.color;
                    color.a = layer.opacity;
                    renderer.material.color = color;
                }
            }
        }
        
        /// <summary>
        /// 设置层激活状态
        /// </summary>
        public void SetLayerActive(string layerName, bool active)
        {
            var layer = GetLayer(layerName);
            if (layer != null)
            {
                layer.isActive = active;
                layer.layerObject.SetActive(active);
            }
        }
        
        /// <summary>
        /// 切换到预设
        /// </summary>
        public void SwitchToPreset(string layerName, EarthboundBackgroundPreset preset, float transitionTime = 0f)
        {
            var layer = GetLayer(layerName);
            if (layer != null && layer.controller != null)
            {
                if (transitionTime <= 0f)
                {
                    // 立即切换
                    layer.controller.ApplyPreset(preset);
                    layer.preset = preset;
                }
                else
                {
                    // 平滑过渡
                    if (_currentTransition != null)
                    {
                        StopCoroutine(_currentTransition);
                    }
                    _currentTransition = StartCoroutine(TransitionToPreset(layer, preset, transitionTime));
                }
            }
        }
        
        /// <summary>
        /// 创建预设组合
        /// </summary>
        public void ApplyPresetCombination(Dictionary<string, EarthboundBackgroundPreset> combination, float transitionTime = 0f)
        {
            foreach (var kvp in combination)
            {
                SwitchToPreset(kvp.Key, kvp.Value, transitionTime);
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 过渡到预设
        /// </summary>
        private System.Collections.IEnumerator TransitionToPreset(BackgroundLayer layer, EarthboundBackgroundPreset targetPreset, float duration)
        {
            onTransitionStart?.Invoke(layer.layerName);
            
            // 保存初始值
            var startTime = Time.time;
            var initialPreset = ScriptableObject.CreateInstance<EarthboundBackgroundPreset>();
            initialPreset.CopyFromController(layer.controller);
            
            while (Time.time - startTime < duration)
            {
                float t = (Time.time - startTime) / duration;
                
                // 插值参数
                layer.controller.SetScrollSpeed(Vector2.Lerp(initialPreset.scrollSpeed, targetPreset.scrollSpeed, t));
                layer.controller.SetWaveParameters(
                    Vector2.Lerp(initialPreset.waveAmplitude, targetPreset.waveAmplitude, t),
                    Vector2.Lerp(initialPreset.waveFrequency, targetPreset.waveFrequency, t),
                    Vector2.Lerp(initialPreset.waveSpeed, targetPreset.waveSpeed, t)
                );
                layer.controller.SetPaletteCycleSpeed(Mathf.Lerp(initialPreset.paletteCycleSpeed, targetPreset.paletteCycleSpeed, t));
                layer.controller.SetColorAdjustment(
                    Mathf.Lerp(initialPreset.brightness, targetPreset.brightness, t),
                    Mathf.Lerp(initialPreset.contrast, targetPreset.contrast, t),
                    Mathf.Lerp(initialPreset.saturation, targetPreset.saturation, t)
                );
                
                yield return null;
            }
            
            // 应用最终预设
            layer.controller.ApplyPreset(targetPreset);
            layer.preset = targetPreset;
            
            // 清理临时预设
            Destroy(initialPreset);
            
            onTransitionComplete?.Invoke(layer.layerName);
            _currentTransition = null;
        }
        
        #endregion
        
        #region 编辑器辅助
        
        #if UNITY_EDITOR
        /// <summary>
        /// 在编辑器中创建预设
        /// </summary>
        [ContextMenu("Create Preset from Current Settings")]
        private void CreatePresetFromCurrentSettings()
        {
            if (_layers.Count == 0) return;
            
            var path = UnityEditor.EditorUtility.SaveFilePanelInProject(
                "Save Background Preset",
                "NewBackgroundPreset",
                "asset",
                "Please enter a file name to save the background preset to"
            );
            
            if (!string.IsNullOrEmpty(path))
            {
                var preset = ScriptableObject.CreateInstance<EarthboundBackgroundPreset>();
                preset.CopyFromController(_layers[0].controller);
                UnityEditor.AssetDatabase.CreateAsset(preset, path);
                UnityEditor.AssetDatabase.SaveAssets();
                UnityEditor.Selection.activeObject = preset;
            }
        }
        #endif
        
        #endregion
    }
}