Shader "UI/EarthboundBackground"
{
    Properties
    {
        [PerRendererData] _MainTex ("Base Texture (BW)", 2D) = "white" {}
        _PaletteTex ("Palette Gradient", 2D) = "white" {}
        
        // 滚动参数
        _ScrollSpeed ("Scroll Speed", Vector) = (0.1, 0.1, 0, 0)
        
        // UV扭曲参数
        _WaveAmplitude ("Wave Amplitude", Vector) = (0.1, 0.1, 0, 0)
        _WaveFrequency ("Wave Frequency", Vector) = (10, 10, 0, 0)
        _WaveSpeed ("Wave Speed", Vector) = (1, 1, 0, 0)
        
        // 调色板循环参数
        _PaletteCycleSpeed ("Palette Cycle Speed", Float) = 1.0
        _PaletteOffset ("Palette Offset", Float) = 0.0
        
        // 额外控制
        _Brightness ("Brightness", Float) = 1.0
        _Contrast ("Contrast", Float) = 1.0
        _Saturation ("Saturation", Float) = 1.0
        
        // UI必需的属性
        _Color ("Tint", Color) = (1,1,1,1)
        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
        _ColorMask ("Color Mask", Float) = 15
        
        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
    }
    
    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }
        
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }
        
        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]
        
        Pass
        {
            Name "Default"
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            
            #include "UnityCG.cginc"
            #include "UnityUI.cginc"
            
            #pragma multi_compile_local _ UNITY_UI_CLIP_RECT
            #pragma multi_compile_local _ UNITY_UI_ALPHACLIP
            
            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };
            
            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float4 worldPosition : TEXCOORD1;
                UNITY_VERTEX_OUTPUT_STEREO
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _PaletteTex;
            fixed4 _Color;
            fixed4 _TextureSampleAdd;
            float4 _ClipRect;
            
            float4 _ScrollSpeed;
            float4 _WaveAmplitude;
            float4 _WaveFrequency;
            float4 _WaveSpeed;
            float _PaletteCycleSpeed;
            float _PaletteOffset;
            float _Brightness;
            float _Contrast;
            float _Saturation;
            
            v2f vert(appdata_t v)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);
                
                OUT.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
                
                OUT.color = v.color * _Color;
                return OUT;
            }
            
            // 辅助函数：调整饱和度
            fixed3 AdjustSaturation(fixed3 color, float saturation)
            {
                float grey = dot(color, float3(0.299, 0.587, 0.114));
                return lerp(grey.xxx, color, saturation);
            }
            
            fixed4 frag(v2f IN) : SV_Target
            {
                // 1. 应用UV扭曲
                float2 distortedUV = IN.texcoord;
                
                // 水平扭曲
                distortedUV.x += sin(IN.texcoord.y * _WaveFrequency.x + _Time.y * _WaveSpeed.x) * _WaveAmplitude.x;
                
                // 垂直扭曲
                distortedUV.y += sin(IN.texcoord.x * _WaveFrequency.y + _Time.y * _WaveSpeed.y) * _WaveAmplitude.y;
                
                // 2. 应用滚动
                distortedUV += _Time.y * _ScrollSpeed.xy;
                
                // 3. 确保UV在0-1范围内循环
                distortedUV = frac(distortedUV);
                
                // 4. 采样黑白纹理
                fixed grayscale = tex2D(_MainTex, distortedUV).r;
                
                // 5. 调色板映射
                float paletteIndex = grayscale + (_Time.y * _PaletteCycleSpeed + _PaletteOffset);
                paletteIndex = frac(paletteIndex);
                
                // 采样调色板
                fixed4 color = tex2D(_PaletteTex, float2(paletteIndex, 0.5));
                
                // 6. 应用亮度和对比度
                color.rgb = ((color.rgb - 0.5) * _Contrast) + 0.5;
                color.rgb *= _Brightness;
                
                // 7. 应用饱和度
                color.rgb = AdjustSaturation(color.rgb, _Saturation);
                
                // 8. 应用UI颜色调制
                color *= IN.color;
                
                // 9. UI裁剪
                #ifdef UNITY_UI_CLIP_RECT
                color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                #endif
                
                #ifdef UNITY_UI_ALPHACLIP
                clip (color.a - 0.001);
                #endif
                
                return color;
            }
            ENDCG
        }
    }
    
    FallBack "UI/Default"
}