Shader "Custom/EarthboundBackground"
{
    Properties
    {
        _MainTex ("Base Texture (BW)", 2D) = "white" {}
        _PaletteTex ("Palette Gradient", 2D) = "white" {}
        
        // 滚动参数
        _ScrollSpeed ("Scroll Speed", Vector) = (0.1, 0.1, 0, 0)
        
        // UV扭曲参数
        _WaveAmplitude ("Wave Amplitude", Vector) = (0.1, 0.1, 0, 0)
        _WaveFrequency ("Wave Frequency", Vector) = (10, 10, 0, 0)
        _WaveSpeed ("Wave Speed", Vector) = (1, 1, 0, 0)
        
        // 调色板循环参数
        _PaletteCycleSpeed ("Palette Cycle Speed", Float) = 1.0
        _PaletteOffset ("Palette Offset", Float) = 0.0
        
        // 额外控制
        _Brightness ("Brightness", Float) = 1.0
        _Contrast ("Contrast", Float) = 1.0
        _Saturation ("Saturation", Float) = 1.0
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType"="Transparent" 
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "PreviewType"="Plane"
        }
        
        LOD 100
        
        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            Cull Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fog
            
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                UNITY_FOG_COORDS(1)
                float4 vertex : SV_POSITION;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _PaletteTex;
            
            float4 _ScrollSpeed;
            float4 _WaveAmplitude;
            float4 _WaveFrequency;
            float4 _WaveSpeed;
            float _PaletteCycleSpeed;
            float _PaletteOffset;
            float _Brightness;
            float _Contrast;
            float _Saturation;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                UNITY_TRANSFER_FOG(o,o.vertex);
                return o;
            }
            
            // 辅助函数：调整饱和度
            fixed3 AdjustSaturation(fixed3 color, float saturation)
            {
                float grey = dot(color, float3(0.299, 0.587, 0.114));
                return lerp(grey.xxx, color, saturation);
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                // 1. 应用UV扭曲
                float2 distortedUV = i.uv;
                
                // 水平扭曲
                distortedUV.x += sin(i.uv.y * _WaveFrequency.x + _Time.y * _WaveSpeed.x) * _WaveAmplitude.x;
                
                // 垂直扭曲
                distortedUV.y += sin(i.uv.x * _WaveFrequency.y + _Time.y * _WaveSpeed.y) * _WaveAmplitude.y;
                
                // 2. 应用滚动
                distortedUV += _Time.y * _ScrollSpeed.xy;
                
                // 3. 确保UV在0-1范围内循环
                distortedUV = frac(distortedUV);
                
                // 4. 采样黑白纹理
                fixed grayscale = tex2D(_MainTex, distortedUV).r;
                
                // 5. 调色板映射
                float paletteIndex = grayscale + (_Time.y * _PaletteCycleSpeed + _PaletteOffset);
                paletteIndex = frac(paletteIndex); // 确保在0-1范围内
                
                // 采样调色板
                fixed4 color = tex2D(_PaletteTex, float2(paletteIndex, 0.5));
                
                // 6. 应用亮度和对比度
                color.rgb = ((color.rgb - 0.5) * _Contrast) + 0.5;
                color.rgb *= _Brightness;
                
                // 7. 应用饱和度
                color.rgb = AdjustSaturation(color.rgb, _Saturation);
                
                // 8. 应用雾效
                UNITY_APPLY_FOG(i.fogCoord, color);
                
                return color;
            }
            ENDCG
        }
    }
    
    FallBack "Sprites/Default"
}