# Earthbound风格背景系统使用指南

## 概述
本系统实现了类似Earthbound游戏的迷幻动画背景效果，包括UV扭曲、滚动、调色板循环等特性。支持2D和3D游戏使用。

## 快速开始

### 2D游戏设置

#### 方式一：使用Sprite（推荐全屏背景）

##### 1. 创建2D背景精灵
```
GameObject → 2D Object → Sprite → Square
命名为 "Background2D"
```

##### 2. 设置Transform
```
Position: (0, 0, 0)  // Z轴在2D中不重要
Scale: (20, 20, 1)   // 根据相机视野调整，覆盖整个屏幕
```

##### 3. 配置Sprite Renderer
1. **Sprite**: 选择白色方块精灵或保持None
2. **Material**: 创建使用`EarthboundBackground`着色器的材质
3. **Sorting Layer**: 设置为"Background"（需要先创建）
4. **Order in Layer**: -1000（确保在最底层）

##### 4. 添加控制器组件
1. 点击"Add Component"
2. 搜索并添加`EarthboundBackgroundController`
3. 控制器会自动管理材质实例

#### 方式二：使用UI Image（推荐UI内嵌背景）

##### 1. 创建UI背景
```
Canvas → 右键 → UI → Image
命名为 "BackgroundUI"
```

##### 2. 设置RectTransform
- **Anchor Presets**: 按住Alt+Shift选择右下角图标（全屏拉伸）
- **Left/Top/Right/Bottom**: 全部设为0（覆盖整个Canvas）

##### 3. 配置Image组件
1. **Source Image**: 留空或设置白色纹理
2. **Material**: 创建使用`UI/EarthboundBackground`着色器的材质
3. **Color**: 保持白色（可用于调节整体透明度）

##### 4. 添加UI控制器组件
1. 点击"Add Component"
2. 搜索并添加`EarthboundBackgroundUIController`（注意是UI版本）
3. 控制器会自动管理材质实例

##### 5. Canvas层级设置
- 创建单独的背景Canvas，Sort Order设为-1
- 或在主Canvas中，确保背景Image在层级最底部

### Sorting Layer设置（Sprite方式需要）
1. Edit → Project Settings → Tags and Layers
2. 在Sorting Layers中添加：
   - Background（最底层）
   - Default
   - Foreground
   - UI（最顶层）

### 3D游戏设置（备选）

#### 1. 创建3D背景对象
```
GameObject → 3D Object → Plane
命名为 "Background3D"
```

#### 2. 设置Transform
```
Position: (0, 0, 10)  // Z轴推后，远离相机
Scale: 根据场景大小调整
```

#### 3. 配置Mesh Renderer
1. 移除不需要的Mesh Collider组件
2. 设置Material为使用`EarthboundBackground`着色器的材质
3. 关闭Cast Shadows（背景不需要投影）

#### 4. 添加控制器组件
同2D设置

### 纹理设置（2D/3D通用）
- **基础纹理**：从`BW`文件夹选择黑白纹理
  - kaleidoscope03：万花筒效果
  - circles05/06：圆圈图案
  - lines01/02：线条图案
  - sea02：海洋波纹
  - squares01/03：方块图案
  - tiles01/05：瓦片图案
- **调色板纹理**：从`Palette`文件夹选择颜色渐变

### 参数调整（2D/3D通用）
- **滚动速度**：控制纹理移动速度
- **波动振幅**：控制扭曲程度
- **波动频率**：控制扭曲密度
- **波动速度**：控制扭曲动画速度
- **调色板循环速度**：控制颜色变化速度

## 2D游戏最佳实践

### 层级管理
```
Sorting Layers（从后到前）：
1. Background     (-1000 ~ -900)  // 远景背景
2. Midground      (-500 ~ -400)   // 中景装饰
3. Default        (0)              // 游戏主体
4. Foreground     (100 ~ 200)      // 前景装饰
5. UI             (1000+)          // UI元素
```

### 2D多层背景示例
```csharp
// 创建多层2D背景
void Create2DLayeredBackground()
{
    // 第一层：缓慢移动的基础背景
    var layer1 = CreateBackgroundLayer("Layer1", -1000);
    layer1.SetScrollSpeed(new Vector2(0.05f, 0.05f));
    
    // 第二层：中速移动的装饰层
    var layer2 = CreateBackgroundLayer("Layer2", -990);
    layer2.SetScrollSpeed(new Vector2(0.1f, 0.1f));
    layer2.SetOpacity(0.7f);
    
    // 第三层：快速移动的前景效果
    var layer3 = CreateBackgroundLayer("Layer3", -980);
    layer3.SetScrollSpeed(new Vector2(0.2f, 0.2f));
    layer3.SetOpacity(0.5f);
}
```

### 2D相机设置建议
```
Main Camera:
- Clear Flags: Solid Color
- Background: 与背景协调的颜色
- Orthographic Size: 根据游戏设计调整
- Culling Mask: 所有层
```

## 预设系统

### 创建预设
1. 调整好背景效果后
2. 在Inspector中点击"保存当前设置为预设"
3. 选择保存位置（建议：`Assets/Game/Scriptables/BackgroundPresets/`）

### 使用预设
1. 在Inspector的"预设管理"部分
2. 拖入预设文件即可应用

### 推荐预设组合
- **万花筒漩涡**
  - 基础纹理：kaleidoscope03
  - 调色板：pinkandgreen
  - 调色板循环速度：2.0
  - 波动振幅：(0.05, 0.05)

- **海洋波动**
  - 基础纹理：sea02
  - 调色板：sunlight
  - 滚动速度：(0.2, 0.1)
  - 波动振幅：(0.1, 0.05)

- **几何网格**
  - 基础纹理：tiles01
  - 调色板：01
  - 滚动速度：(0.3, -0.3)
  - 波动频率：(5, 5)

## 多层背景

### 设置多层背景
1. 创建空GameObject作为管理器
2. 添加`EarthboundBackgroundManager`组件
3. 为每层创建子对象并添加控制器
4. 在管理器中配置层设置

### 层混合技巧
- 使用不同的滚动速度创造深度感
- 调整透明度实现层叠效果
- 组合不同纹理类型增加复杂度

## 性能优化建议

### 2D游戏优化
1. **使用Sprite Renderer批处理**
   - 确保背景使用相同的材质实例
   - 利用Sorting Layer而非Z轴排序
   
2. **纹理设置**
   - 使用低分辨率纹理（256x256足够）
   - 设置纹理压缩格式为RGB Compressed
   
3. **Draw Call优化**
   - 多层背景共享材质，通过参数区分
   - 使用Material Property Block避免材质实例化

### 3D游戏优化
1. 使用低分辨率纹理（推荐256x256）
2. 限制同时激活的层数（建议不超过3层）
3. 在移动平台上降低波动频率
4. 使用LOD系统远距离时简化效果

### 通用优化
1. **移动平台**
   - 降低波动频率到5-8
   - 减少振幅到0.05-0.1
   - 使用更简单的纹理图案
   
2. **内存优化**
   - 共享纹理资源
   - 使用对象池管理多个背景层

## 2D游戏特别注意

### Sprite设置要点
1. **如果Sprite为None**
   - 需要创建或指定一个白色方块精灵
   - 或使用Unity默认的白色精灵

2. **Scale调整**
   - 根据Camera的Orthographic Size调整
   - 公式：Scale = Camera.orthographicSize * 2 * aspect ratio

3. **材质共享**
   - 多个背景层可以共享同一个材质
   - 通过控制器参数区分效果

4. **与UI的配合**
   - 确保背景的Sorting Layer低于所有游戏元素
   - UI Canvas的Sorting Layer应该最高

## 常见问题

### Q: 2D精灵背景显示不出来？
A: 检查Sprite是否设置，Scale是否足够大，Sorting Layer是否正确

### Q: 背景看起来太亮/太暗？
A: 调整"亮度"和"对比度"参数

### Q: 颜色变化太快？
A: 降低"调色板循环速度"

### Q: 扭曲效果太强？
A: 减小"波动振幅"值

### Q: 如何创建静态背景？
A: 将所有速度参数设为0

### Q: 2D背景遮挡了游戏对象？
A: 检查Sorting Layer设置，确保背景层在最底部

### Q: 在UI Image上添加控制器显示错误？
A: 使用`EarthboundBackgroundUIController`而不是普通的`EarthboundBackgroundController`

### Q: UI背景和Sprite背景选哪个？
A: 全屏背景用Sprite（性能好），UI内嵌或需要遮罩时用Image（功能多）

## 示例代码

### 2D游戏示例

```csharp
// 2D背景初始化
public class Background2DSetup : MonoBehaviour
{
    void Start()
    {
        // 创建2D背景精灵
        GameObject bg = new GameObject("Background2D");
        SpriteRenderer sr = bg.AddComponent<SpriteRenderer>();
        
        // 设置Sprite Renderer
        sr.sprite = whiteSquareSprite; // 或使用默认精灵
        sr.sortingLayerName = "Background";
        sr.sortingOrder = -1000;
        
        // 添加控制器
        var controller = bg.AddComponent<EarthboundBackgroundController>();
        
        // 设置材质
        Material mat = new Material(Shader.Find("Custom/EarthboundBackground"));
        sr.material = mat;
        
        // 调整大小覆盖屏幕
        bg.transform.localScale = new Vector3(20, 20, 1);
    }
}

// 2D场景切换效果
public void OnEnterBossArea()
{
    var controller = background.GetComponent<EarthboundBackgroundController>();
    
    // 切换到激烈的Boss战背景
    controller.SetWaveParameters(
        new Vector2(0.3f, 0.3f),  // 大振幅
        new Vector2(20f, 20f),    // 高频率
        new Vector2(3f, 3f)       // 快速度
    );
    controller.SetPaletteCycleSpeed(5f); // 快速颜色切换
}

// UI背景淡入淡出效果
public IEnumerator FadeUIBackground(float targetAlpha, float duration)
{
    var uiController = backgroundImage.GetComponent<EarthboundBackgroundUIController>();
    var image = backgroundImage.GetComponent<Image>();
    float startAlpha = image.color.a;
    float elapsed = 0;
    
    while (elapsed < duration)
    {
        elapsed += Time.deltaTime;
        float t = elapsed / duration;
        uiController.SetAlpha(Mathf.Lerp(startAlpha, targetAlpha, t));
        yield return null;
    }
}
```

### 3D游戏示例

```csharp
// 动态切换预设
public void SwitchToIntenseMode()
{
    var controller = GetComponent<EarthboundBackgroundController>();
    controller.SetWaveParameters(
        new Vector2(0.2f, 0.2f),  // 振幅
        new Vector2(15f, 15f),    // 频率
        new Vector2(2f, 2f)       // 速度
    );
    controller.SetPaletteCycleSpeed(3f);
}
```

### 通用多层管理

```csharp
// 使用管理器控制多层
public void CreateDreamSequence()
{
    var manager = GetComponent<EarthboundBackgroundManager>();
    manager.SetLayerOpacity("Layer1", 0.7f);
    manager.SetLayerOpacity("Layer2", 0.5f);
    manager.SwitchToPreset("Layer1", dreamPreset, 2f);
}
```

## 进阶技巧

### 2D游戏进阶
1. **视差滚动效果**
   ```csharp
   // 根据玩家移动创建视差效果
   void Update()
   {
       float parallaxX = player.position.x * 0.1f;
       backgroundController.SetScrollSpeed(new Vector2(parallaxX, 0));
   }
   ```

2. **区域过渡效果**
   - 进入不同区域时平滑切换背景预设
   - 使用协程实现渐变过渡

3. **动态响应系统**
   - Boss血量低时加速背景动画
   - 危险时刻增加扭曲效果

### 通用进阶技巧
1. **事件触发效果**：根据游戏事件动态调整参数
2. **音乐同步**：将参数与音乐节奏绑定
3. **自定义调色板**：创建独特的颜色渐变
4. **混合模式**：尝试不同的Shader混合模式

## 总结

本系统提供了三种灵活的Earthbound风格背景实现方式：

### 使用方式对比
| 方式 | 组件 | 控制器 | 适用场景 | 优势 |
|-----|------|--------|---------|------|
| **2D Sprite** | Sprite Renderer | EarthboundBackgroundController | 全屏游戏背景 | 性能最佳，Sorting Layer控制 |
| **UI Image** | Image | EarthboundBackgroundUIController | UI内嵌背景、对话框背景 | 自适应分辨率，支持遮罩 |
| **3D Plane** | Mesh Renderer | EarthboundBackgroundController | 3D游戏背景 | 3D空间定位 |

对于2D游戏，推荐根据具体需求选择Sprite或UI Image方式。充分利用预设系统可以快速创建和切换不同的视觉效果，提升游戏的视觉体验。